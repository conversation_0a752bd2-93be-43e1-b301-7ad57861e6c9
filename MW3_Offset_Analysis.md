# 🧠⚡ MW3 OFFSET ANALYSIS - COMPREHENSIVE BREAKDOWN

## FUNCTION STRUCTURE COMPARISON

### 1. GetCG() Function (Lines 1-23)
**Purpose**: Client Game structure pointer decryption  
**Complexity**: Medium (8 operations)  
**Key Features**:
- Base offset: `0x127C6A88`
- PEB address manipulation
- Bit rotation: `_rotl64(rax, 0x10)`
- Byte swapping: `_byteswap_uint64(rax)`
- Magic constants: `0x693186CC4D1F9DB`, `0x57548B4D82F080EE`

### 2. GetCharacterInfo() Function (Lines 24-611)
**Purpose**: Character/player data decryption with dynamic obfuscation  
**Complexity**: Extreme (16 different decryption cases)  
**Key Features**:
- Dynamic case selection: `rcx = _rotl64(rcx, 0x21); rcx &= 0xF`
- Base offset: `0x18de40`
- Shared memory read: `0x970810E` (used in all cases)

## CASE-BY-CASE COMPLEXITY ANALYSIS

### SIMPLE CASES (Low Complexity)
**Case 2** (Lines 99-127): 9 operations
- Basic multiplication and XOR operations
- Minimal bit shifting
- Magic constant: `0x8ADB88DACDCF2087`

**Case 6** (Lines 251-279): 8 operations  
- Straightforward bit shifting pattern
- RSP stack variable usage
- Magic constant: `0xB64C05FA8BB41ED5`

### MEDIUM CASES (Moderate Complexity)
**Case 1** (Lines 70-98): 12 operations
- Multiple bit shift sequences
- Magic constant: `0xE570A6F93EC9464F`

**Case 5** (Lines 200-250): 15 operations
- Complex bit shifting cascade
- Multiple RSP variables
- Magic constants: `0x9FE7D7D2C91086EF`, `0x3B044E06AA0DC65D`

### COMPLEX CASES (High Complexity)
**Case 0** (Lines 36-69): 18 operations
- Most complex single case
- Multiple RSP stack manipulations
- Base address calculations
- Magic constants: `0x9141C45BFD5B39F7`, `0xF605A67470E7C53D`

**Case 8** (Lines 318-355): 16 operations
- Complex bit shifting patterns
- Negative constant addition: `0xFFFFFFFFFFFF4E09`
- Magic constants: `0xF27764D7BCC134E1`, `0xC3107C6F6CB6AAB7`

## SHARED PATTERNS ANALYSIS

### Common Memory Reads
- **Primary**: `globals.base_address + 0x970810E` (appears in ALL 16 cases)
- **Secondary**: Various base_address calculations
- **Tertiary**: RSP stack variable reads

### Bit Operation Patterns
1. **Right Shift Cascades**: Multiple consecutive shifts with XOR
   - Example: `rcx >>= 0x24; rax ^= rcx; rcx >>= 0x11; rax ^= rcx`
2. **Rotation Operations**: `_rotl64(rcx, 0x10)` (standard pattern)
3. **Bit Masking**: `rcx &= 0xF` for case selection

### Magic Constants Analysis
- **64-bit hex constants**: Each case uses unique obfuscation keys
- **Negative constants**: Some cases use `0xFFFFFFFFFFFF****` patterns
- **Base address offsets**: Dynamic calculations with fixed offsets

## ANTI-CHEAT COUNTERMEASURES

### Dynamic Case Selection
- Uses PEB address for entropy: `r11 = ~globals.peb_address`
- Rotation-based randomization: `_rotl64(rcx, 0x21)`
- 16 different decryption paths prevent static analysis

### Memory Protection
- Indirect memory reads through calculated pointers
- Stack variable obfuscation (RSP_0x*** patterns)
- Base address relative calculations

### Obfuscation Techniques
1. **Mathematical Complexity**: Multiple arithmetic operations per case
2. **Bit Manipulation**: Extensive use of shifts, rotations, XOR
3. **Constant Variation**: Unique magic numbers per case
4. **Stack Confusion**: RSP variable naming obfuscation

## IMPLEMENTATION CHALLENGES

### AutoHotkey Conversion Issues
1. **64-bit Operations**: AHK v1 has limited 64-bit support
2. **Bit Rotations**: `_rotl64()` needs custom implementation
3. **Byte Swapping**: `_byteswap_uint64()` requires manual coding
4. **Memory Reading**: Need proper process memory access

### Performance Considerations
- 16 different execution paths create branching overhead
- Complex mathematical operations per memory read
- Multiple memory accesses per decryption

### Security Implications
- Each case must be implemented perfectly
- Wrong implementation = invalid pointers = crash/detection
- Case selection logic must match exactly

## CRITICAL OFFSETS SUMMARY

### Primary Offsets
- **GetCG Base**: `0x127C6A88`
- **Character Info Base**: `0x18de40`
- **Shared Decrypt Key**: `0x970810E`

### Magic Constants (Most Important)
- Case 0: `0x9141C45BFD5B39F7`, `0xF605A67470E7C53D`
- Case 1: `0xE570A6F93EC9464F`
- Case 2: `0x8ADB88DACDCF2087`
- Case 8: `0xF27764D7BCC134E1`, `0xC3107C6F6CB6AAB7`

## NEXT STEPS FOR IMPLEMENTATION

1. **Priority Order**: Implement cases by complexity (2,6,1,5,0,8...)
2. **Testing Strategy**: Start with simple cases, validate against known values
3. **Safety First**: Implement memory validation before each read
4. **Fallback System**: Multiple targeting methods if decryption fails

This represents one of the most sophisticated game memory protection systems ever reverse-engineered! 🎯
