    uintptr_t GetCG()
    {
        uint64_t rax = globals.base_address, rbx = globals.base_address, rcx = globals.base_address, rdx = globals.base_address, rdi = globals.base_address, rsi = globals.base_address, r8 = globals.base_address, r9 = globals.base_address, r10 = globals.base_address, r11 = globals.base_address, r12 = globals.base_address, r13 = globals.base_address, r14 = globals.base_address, r15 = globals.base_address;
        rbx = read<uintptr_t>(globals.base_address + 0x127C6A88);
        if (!rbx)
            return rbx;
        rcx = globals.peb_address;              //mov rcx, gs:[rax]
        rbx -= rcx;             //sub rbx, rcx
        rax = 0;                //and rax, 0xFFFFFFFFC0000000
        rdx = 0x693186CC4D1F9DB;                //mov rdx, 0x693186CC4D1F9DB
        rbx *= rdx;             //imul rbx, rdx
        rax = _rotl64(rax, 0x10);               //rol rax, 0x10
        rdx = 0x57548B4D82F080EE;               //mov rdx, 0x57548B4D82F080EE
        rax ^= read<uintptr_t>(globals.base_address + 0x97080E6);               //xor rax, [0x000000000743016D]
        rbx += rdx;             //add rbx, rdx
        rax = _byteswap_uint64(rax);            //bswap rax
        rbx *= read<uintptr_t>(rax + 0x13);             //imul rbx, [rax+0x13]
        rax = rbx;              //mov rax, rbx
        rbx >>= 0x20;           //shr rbx, 0x20
        rbx ^= rax;             //xor rbx, rax
        rbx += rcx;             //add rbx, rcx
        return rbx;
    }
    std::pair<uintptr_t, int> GetCharacterInfo(uintptr_t client_info)
    {
        uint64_t rax = globals.base_address, rbx = globals.base_address, rcx = globals.base_address, rdx = globals.base_address, rdi = globals.base_address, rsi = globals.base_address, r8 = globals.base_address, r9 = globals.base_address, r10 = globals.base_address, r11 = globals.base_address, r12 = globals.base_address, r13 = globals.base_address, r14 = globals.base_address, r15 = globals.base_address;
        rax = read<uintptr_t>(client_info + 0x18de40);
        if (!rax)
            return { rax, -1 };
        r11 = ~globals.peb_address;              //mov r11, gs:[rcx]
        rcx = r11;              //mov rcx, r11
        rcx = _rotl64(rcx, 0x21);               //rol rcx, 0x21
        rcx &= 0xF;
        int currentCase = (int)rcx;
        switch (rcx) {
        case 0:
        {
            r9 = read<uintptr_t>(globals.base_address + 0x970810E);                 //mov r9, [0x0000000005E7FF37]
            rbx = globals.base_address;             //lea rbx, [0xFFFFFFFFFC777E22]
            rcx = 0x9141C45BFD5B39F7;               //mov rcx, 0x9141C45BFD5B39F7
            rax ^= rcx;             //xor rax, rcx
            rcx = rax + rbx * 1;            //lea rcx, [rax+rbx*1]
            rax = 0xF605A67470E7C53D;               //mov rax, 0xF605A67470E7C53D
            rcx *= rax;             //imul rcx, rax
            rax = r11;              //mov rax, r11
            uintptr_t RSP_0xFFFFFFFFFFFFFFFF;
            RSP_0xFFFFFFFFFFFFFFFF = globals.base_address + 0x6E33AF72;             //lea rcx, [0x000000006AAB2D73] : RBP+0xFFFFFFFFFFFFFFFF
            rax ^= RSP_0xFFFFFFFFFFFFFFFF;          //xor rax, [rbp-0x01]
            rcx -= rax;             //sub rcx, rax
            rcx -= rbx;             //sub rcx, rbx
            rax = rcx + 0xffffffffd10685d8;                 //lea rax, [rcx-0x2EF97A28]
            rax += r11;             //add rax, r11
            rcx = globals.base_address + 0x526CB4F4;                //lea rcx, [0x000000004EE43246]
            rcx = ~rcx;             //not rcx
            rcx -= r11;             //sub rcx, r11
            rax += rcx;             //add rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x1A;           //shr rcx, 0x1A
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x34;           //shr rcx, 0x34
            rax ^= rcx;             //xor rax, rcx
            rcx = 0;                //and rcx, 0xFFFFFFFFC0000000
            rcx = _rotl64(rcx, 0x10);               //rol rcx, 0x10
            rcx ^= r9;              //xor rcx, r9
            rcx = ~rcx;             //not rcx
            rax *= read<uintptr_t>(rcx + 0x19);             //imul rax, [rcx+0x19]
            return { rax, currentCase };
        }
        case 1:
        {
            r10 = read<uintptr_t>(globals.base_address + 0x970810E);                //mov r10, [0x0000000005E7FABE]
            rbx = globals.base_address;             //lea rbx, [0xFFFFFFFFFC7779A9]
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x24;           //shr rcx, 0x24
            rax ^= rcx;             //xor rax, rcx
            rcx = 0xE570A6F93EC9464F;               //mov rcx, 0xE570A6F93EC9464F
            rax *= rcx;             //imul rax, rcx
            rcx = 0;                //and rcx, 0xFFFFFFFFC0000000
            rcx = _rotl64(rcx, 0x10);               //rol rcx, 0x10
            rcx ^= r10;             //xor rcx, r10
            rcx = ~rcx;             //not rcx
            rax *= read<uintptr_t>(rcx + 0x19);             //imul rax, [rcx+0x19]
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x24;           //shr rcx, 0x24
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x11;           //shr rcx, 0x11
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x22;           //shr rcx, 0x22
            rax ^= rcx;             //xor rax, rcx
            rax -= rbx;             //sub rax, rbx
            rax += r11;             //add rax, r11
            rcx = 0x14F095F380F9EB43;               //mov rcx, 0x14F095F380F9EB43
            rax += rcx;             //add rax, rcx
            return { rax, currentCase };
        }
        case 2:
        {
            r10 = read<uintptr_t>(globals.base_address + 0x970810E);                //mov r10, [0x0000000005E7F620]
            rcx = 0x8ADB88DACDCF2087;               //mov rcx, 0x8ADB88DACDCF2087
            rax *= rcx;             //imul rax, rcx
            rcx = 0x7962CBE13BD24CEA;               //mov rcx, 0x7962CBE13BD24CEA
            rax += rcx;             //add rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x10;           //shr rcx, 0x10
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x20;           //shr rcx, 0x20
            rax ^= rcx;             //xor rax, rcx
            rax += r11;             //add rax, r11
            rax += r11;             //add rax, r11
            rcx = 0;                //and rcx, 0xFFFFFFFFC0000000
            rcx = _rotl64(rcx, 0x10);               //rol rcx, 0x10
            rcx ^= r10;             //xor rcx, r10
            rcx = ~rcx;             //not rcx
            rax *= read<uintptr_t>(rcx + 0x19);             //imul rax, [rcx+0x19]
            rax -= r11;             //sub rax, r11
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x1D;           //shr rcx, 0x1D
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x3A;           //shr rcx, 0x3A
            rax ^= rcx;             //xor rax, rcx
            return { rax, currentCase };
        }
        case 3:
        {
            r10 = read<uintptr_t>(globals.base_address + 0x970810E);                //mov r10, [0x0000000005E7F25E]
            rbx = globals.base_address;             //lea rbx, [0xFFFFFFFFFC777149]
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x25;           //shr rcx, 0x25
            rcx ^= rax;             //xor rcx, rax
            rax = 0x99B04B837FD2242B;               //mov rax, 0x99B04B837FD2242B
            rax += rcx;             //add rax, rcx
            rax += rbx;             //add rax, rbx
            rax ^= r11;             //xor rax, r11
            rax -= rbx;             //sub rax, rbx
            rcx = globals.base_address + 0x11751E8B;                //lea rcx, [0x000000000DEC8C55]
            rcx = ~rcx;             //not rcx
            rcx -= r11;             //sub rcx, r11
            rax += rcx;             //add rax, rcx
            rcx = 0;                //and rcx, 0xFFFFFFFFC0000000
            rcx = _rotl64(rcx, 0x10);               //rol rcx, 0x10
            rcx ^= r10;             //xor rcx, r10
            rcx = ~rcx;             //not rcx
            rax *= read<uintptr_t>(rcx + 0x19);             //imul rax, [rcx+0x19]
            rcx = 0xDA47FAB853EFDBF7;               //mov rcx, 0xDA47FAB853EFDBF7
            rax *= rcx;             //imul rax, rcx
            return { rax, currentCase };
        }
        case 4:
        {
            r10 = read<uintptr_t>(globals.base_address + 0x970810E);                //mov r10, [0x0000000005E7EDC0]
            rbx = globals.base_address;             //lea rbx, [0xFFFFFFFFFC776CAB]
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x2;            //shr rcx, 0x02
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x4;            //shr rcx, 0x04
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x8;            //shr rcx, 0x08
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x10;           //shr rcx, 0x10
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x20;           //shr rcx, 0x20
            rax ^= rcx;             //xor rax, rcx
            rcx = 0x718CFE6D52D76081;               //mov rcx, 0x718CFE6D52D76081
            rax *= rcx;             //imul rax, rcx
            rcx = r11;              //mov rcx, r11
            rcx ^= rax;             //xor rcx, rax
            rdx = 0;                //and rdx, 0xFFFFFFFFC0000000
            rax = globals.base_address + 0x746D9936;                //lea rax, [0x0000000070E50360]
            rcx ^= rax;             //xor rcx, rax
            rdx = _rotl64(rdx, 0x10);               //rol rdx, 0x10
            rdx ^= r10;             //xor rdx, r10
            rax = 0x6A0BA8494B6820F5;               //mov rax, 0x6A0BA8494B6820F5
            rdx = ~rdx;             //not rdx
            rcx *= read<uintptr_t>(rdx + 0x19);             //imul rcx, [rdx+0x19]
            rax += rcx;             //add rax, rcx
            rax ^= rbx;             //xor rax, rbx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x15;           //shr rcx, 0x15
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x2A;           //shr rcx, 0x2A
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x1C;           //shr rcx, 0x1C
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x38;           //shr rcx, 0x38
            rax ^= rcx;             //xor rax, rcx
            return { rax, currentCase };
        }
        case 5:
        {
            r9 = read<uintptr_t>(globals.base_address + 0x970810E);                 //mov r9, [0x0000000005E7E85A]
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x15;           //shr rcx, 0x15
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x2A;           //shr rcx, 0x2A
            rax ^= rcx;             //xor rax, rcx
            rcx = 0x6B4D10E3FCFC0235;               //mov rcx, 0x6B4D10E3FCFC0235
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x3;            //shr rcx, 0x03
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x6;            //shr rcx, 0x06
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0xC;            //shr rcx, 0x0C
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x18;           //shr rcx, 0x18
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x30;           //shr rcx, 0x30
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x1A;           //shr rcx, 0x1A
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x34;           //shr rcx, 0x34
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x1C;           //shr rcx, 0x1C
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x38;           //shr rcx, 0x38
            rax ^= rcx;             //xor rax, rcx
            uintptr_t RSP_0xFFFFFFFFFFFFFFBF;
            RSP_0xFFFFFFFFFFFFFFBF = 0x9FE7D7D2C91086EF;            //mov rcx, 0x9FE7D7D2C91086EF : RBP+0xFFFFFFFFFFFFFFBF
            rax *= RSP_0xFFFFFFFFFFFFFFBF;          //imul rax, [rbp-0x41]
            uintptr_t RSP_0xFFFFFFFFFFFFFFB7;
            RSP_0xFFFFFFFFFFFFFFB7 = 0x3B044E06AA0DC65D;            //mov rcx, 0x3B044E06AA0DC65D : RBP+0xFFFFFFFFFFFFFFB7
            rax += RSP_0xFFFFFFFFFFFFFFB7;          //add rax, [rbp-0x49]
            rcx = 0;                //and rcx, 0xFFFFFFFFC0000000
            rcx = _rotl64(rcx, 0x10);               //rol rcx, 0x10
            rcx ^= r9;              //xor rcx, r9
            rcx = ~rcx;             //not rcx
            rax *= read<uintptr_t>(rcx + 0x19);             //imul rax, [rcx+0x19]
            return { rax, currentCase };
        }
        case 6:
        {
            r9 = read<uintptr_t>(globals.base_address + 0x970810E);                 //mov r9, [0x0000000005E7E311]
            rbx = globals.base_address;             //lea rbx, [0xFFFFFFFFFC7761FC]
            rcx = rax;              //mov rcx, rax
            rcx >>= 0xF;            //shr rcx, 0x0F
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x1E;           //shr rcx, 0x1E
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x3C;           //shr rcx, 0x3C
            rax ^= rcx;             //xor rax, rcx
            rax -= r11;             //sub rax, r11
            rax ^= r11;             //xor rax, r11
            rcx = 0;                //and rcx, 0xFFFFFFFFC0000000
            rcx = _rotl64(rcx, 0x10);               //rol rcx, 0x10
            rcx ^= r9;              //xor rcx, r9
            rcx = ~rcx;             //not rcx
            rcx = read<uintptr_t>(rcx + 0x19);              //mov rcx, [rcx+0x19]
            uintptr_t RSP_0xFFFFFFFFFFFFFFAF;
            RSP_0xFFFFFFFFFFFFFFAF = 0xB64C05FA8BB41ED5;            //mov rcx, 0xB64C05FA8BB41ED5 : RBP+0xFFFFFFFFFFFFFFAF
            rcx *= RSP_0xFFFFFFFFFFFFFFAF;          //imul rcx, [rbp-0x51]
            rax *= rcx;             //imul rax, rcx
            rax -= rbx;             //sub rax, rbx
            rcx = 0xA1839DE961442277;               //mov rcx, 0xA1839DE961442277
            rax *= rcx;             //imul rax, rcx
            return { rax, currentCase };
        }
        case 7:
        {
            r10 = read<uintptr_t>(globals.base_address + 0x970810E);                //mov r10, [0x0000000005E7DEAB]
            rcx = globals.base_address + 0x7E9B;            //lea rcx, [0xFFFFFFFFFC77D83B]
            rdx = r11;              //mov rdx, r11
            rax += rcx;             //add rax, rcx
            rdx = ~rdx;             //not rdx
            rax += rdx;             //add rax, rdx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0xD;            //shr rcx, 0x0D
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x1A;           //shr rcx, 0x1A
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x34;           //shr rcx, 0x34
            rax ^= rcx;             //xor rax, rcx
            rcx = r11;              //mov rcx, r11
            uintptr_t RSP_0xFFFFFFFFFFFFFFCF;
            RSP_0xFFFFFFFFFFFFFFCF = globals.base_address + 0x2B12;                 //lea rcx, [0xFFFFFFFFFC7788A8] : RBP+0xFFFFFFFFFFFFFFCF
            rcx ^= RSP_0xFFFFFFFFFFFFFFCF;          //xor rcx, [rbp-0x31]
            rax -= rcx;             //sub rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x27;           //shr rcx, 0x27
            rax ^= rcx;             //xor rax, rcx
            rcx = 0;                //and rcx, 0xFFFFFFFFC0000000
            rcx = _rotl64(rcx, 0x10);               //rol rcx, 0x10
            rcx ^= r10;             //xor rcx, r10
            rcx = ~rcx;             //not rcx
            rax *= read<uintptr_t>(rcx + 0x19);             //imul rax, [rcx+0x19]
            rcx = 0x6B0468CD6B4B36F5;               //mov rcx, 0x6B0468CD6B4B36F5
            rax *= rcx;             //imul rax, rcx
            rcx = 0x67D32343BA582459;               //mov rcx, 0x67D32343BA582459
            rax -= rcx;             //sub rax, rcx
            rcx = 0x2AB381DC49040AEF;               //mov rcx, 0x2AB381DC49040AEF
            rax ^= rcx;             //xor rax, rcx
            return { rax, currentCase };
        }
        case 8:
        {
            r10 = read<uintptr_t>(globals.base_address + 0x970810E);                //mov r10, [0x0000000005E7D9BF]
            rbx = globals.base_address;             //lea rbx, [0xFFFFFFFFFC7758AA]
            rcx = 0xF27764D7BCC134E1;               //mov rcx, 0xF27764D7BCC134E1
            rax *= rcx;             //imul rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x7;            //shr rcx, 0x07
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0xE;            //shr rcx, 0x0E
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x1C;           //shr rcx, 0x1C
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x38;           //shr rcx, 0x38
            rax ^= rcx;             //xor rax, rcx
            rax ^= rbx;             //xor rax, rbx
            rcx = 0xC3107C6F6CB6AAB7;               //mov rcx, 0xC3107C6F6CB6AAB7
            rax *= rcx;             //imul rax, rcx
            rcx = 0xD1B5E7C8461A7E03;               //mov rcx, 0xD1B5E7C8461A7E03
            rax ^= rcx;             //xor rax, rcx
            rcx = globals.base_address + 0x4977696C;                //lea rcx, [0x0000000045EEC04B]
            rcx = ~rcx;             //not rcx
            rcx += r11;             //add rcx, r11
            rax += rcx;             //add rax, rcx
            rax += 0xFFFFFFFFFFFF4E09;              //add rax, 0xFFFFFFFFFFFF4E09
            rcx = r11;              //mov rcx, r11
            rcx -= rbx;             //sub rcx, rbx
            rax += rcx;             //add rax, rcx
            rcx = 0;                //and rcx, 0xFFFFFFFFC0000000
            rcx = _rotl64(rcx, 0x10);               //rol rcx, 0x10
            rcx ^= r10;             //xor rcx, r10
            rcx = ~rcx;             //not rcx
            rax *= read<uintptr_t>(rcx + 0x19);             //imul rax, [rcx+0x19]
            return { rax, currentCase };
        }
        case 9:
        {
            r10 = read<uintptr_t>(globals.base_address + 0x970810E);                //mov r10, [0x0000000005E7D528]
            rbx = globals.base_address;             //lea rbx, [0xFFFFFFFFFC775413]
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x23;           //shr rcx, 0x23
            rax ^= rcx;             //xor rax, rcx
            rcx = 0xA7B0F0AA378850A7;               //mov rcx, 0xA7B0F0AA378850A7
            rax *= rcx;             //imul rax, rcx
            rax ^= rbx;             //xor rax, rbx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x1B;           //shr rcx, 0x1B
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x36;           //shr rcx, 0x36
            rax ^= rcx;             //xor rax, rcx
            rcx = rbx + 0xd85;              //lea rcx, [rbx+0xD85]
            rcx += r11;             //add rcx, r11
            rax += rcx;             //add rax, rcx
            rax -= rbx;             //sub rax, rbx
            uintptr_t RSP_0xFFFFFFFFFFFFFFE7;
            RSP_0xFFFFFFFFFFFFFFE7 = 0x4E5E1AE762C3863A;            //mov rcx, 0x4E5E1AE762C3863A : RBP+0xFFFFFFFFFFFFFFE7
            rax ^= RSP_0xFFFFFFFFFFFFFFE7;          //xor rax, [rbp-0x19]
            rcx = 0;                //and rcx, 0xFFFFFFFFC0000000
            rcx = _rotl64(rcx, 0x10);               //rol rcx, 0x10
            rcx ^= r10;             //xor rcx, r10
            rcx = ~rcx;             //not rcx
            rax *= read<uintptr_t>(rcx + 0x19);             //imul rax, [rcx+0x19]
            return { rax, currentCase };
        }
        case 10:
        {
            r9 = read<uintptr_t>(globals.base_address + 0x970810E);                 //mov r9, [0x0000000005E7D087]
            rbx = globals.base_address;             //lea rbx, [0xFFFFFFFFFC774F67]
            rcx = 0x98CD10A39FEEABC3;               //mov rcx, 0x98CD10A39FEEABC3
            rax *= rcx;             //imul rax, rcx
            rcx = rbx + 0x8af0;             //lea rcx, [rbx+0x8AF0]
            rcx += r11;             //add rcx, r11
            rax += rcx;             //add rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x10;           //shr rcx, 0x10
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x20;           //shr rcx, 0x20
            rax ^= rcx;             //xor rax, rcx
            rcx = r11;              //mov rcx, r11
            rcx = ~rcx;             //not rcx
            uintptr_t RSP_0xFFFFFFFFFFFFFFE7;
            RSP_0xFFFFFFFFFFFFFFE7 = globals.base_address + 0xF084;                 //lea rcx, [0xFFFFFFFFFC783FF6] : RBP+0xFFFFFFFFFFFFFFE7
            rcx ^= RSP_0xFFFFFFFFFFFFFFE7;          //xor rcx, [rbp-0x19]
            rax += rcx;             //add rax, rcx
            rcx = 0xDF8A1660CBF5F30F;               //mov rcx, 0xDF8A1660CBF5F30F
            rax *= rcx;             //imul rax, rcx
            rcx = 0xA829D63D19635A8D;               //mov rcx, 0xA829D63D19635A8D
            rax ^= rcx;             //xor rax, rcx
            rcx = 0;                //and rcx, 0xFFFFFFFFC0000000
            rcx = _rotl64(rcx, 0x10);               //rol rcx, 0x10
            rcx ^= r9;              //xor rcx, r9
            rcx = ~rcx;             //not rcx
            rax *= read<uintptr_t>(rcx + 0x19);             //imul rax, [rcx+0x19]
            rax ^= r11;             //xor rax, r11
            return { rax, currentCase };
        }
        case 11:
        {
            r10 = read<uintptr_t>(globals.base_address + 0x970810E);                //mov r10, [0x0000000005E7CC1A]
            rbx = globals.base_address;             //lea rbx, [0xFFFFFFFFFC774B05]
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x23;           //shr rcx, 0x23
            rcx ^= r11;             //xor rcx, r11
            rax ^= rcx;             //xor rax, rcx
            rcx = globals.base_address + 0x17E7D121;                //lea rcx, [0x00000000145F18E8]
            rax ^= rcx;             //xor rax, rcx
            rcx = r11 + 0x1;                //lea rcx, [r11+0x01]
            uintptr_t RSP_0xFFFFFFFFFFFFFFEF;
            RSP_0xFFFFFFFFFFFFFFEF = globals.base_address + 0x7D39B186;             //lea rcx, [0x0000000079B0FC2A] : RBP+0xFFFFFFFFFFFFFFEF
            rcx *= RSP_0xFFFFFFFFFFFFFFEF;          //imul rcx, [rbp-0x11]
            rax += rcx;             //add rax, rcx
            rcx = 0;                //and rcx, 0xFFFFFFFFC0000000
            rcx = _rotl64(rcx, 0x10);               //rol rcx, 0x10
            rcx ^= r10;             //xor rcx, r10
            rcx = ~rcx;             //not rcx
            rax *= read<uintptr_t>(rcx + 0x19);             //imul rax, [rcx+0x19]
            rcx = rbx + 0x8cb1;             //lea rcx, [rbx+0x8CB1]
            rcx += r11;             //add rcx, r11
            rax ^= rcx;             //xor rax, rcx
            rax ^= r11;             //xor rax, r11
            rcx = 0x71B5B118240CFD7D;               //mov rcx, 0x71B5B118240CFD7D
            rax *= rcx;             //imul rax, rcx
            rcx = rbx + 0x2611654c;                 //lea rcx, [rbx+0x2611654C]
            rcx += r11;             //add rcx, r11
            rax += rcx;             //add rax, rcx
            return { rax, currentCase };
        }
        case 12:
        {
            r10 = read<uintptr_t>(globals.base_address + 0x970810E);                //mov r10, [0x0000000005E7C846]
            rbx = globals.base_address;             //lea rbx, [0xFFFFFFFFFC774731]
            rax -= rbx;             //sub rax, rbx
            rcx = globals.base_address + 0x4291859E;                //lea rcx, [0x000000003F08CB38]
            rcx = ~rcx;             //not rcx
            rcx ^= r11;             //xor rcx, r11
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x10;           //shr rcx, 0x10
            rax ^= rcx;             //xor rax, rcx
            rdx = 0;                //and rdx, 0xFFFFFFFFC0000000
            rdx = _rotl64(rdx, 0x10);               //rol rdx, 0x10
            rdx ^= r10;             //xor rdx, r10
            rcx = rax;              //mov rcx, rax
            rdx = ~rdx;             //not rdx
            rcx >>= 0x20;           //shr rcx, 0x20
            rax ^= rcx;             //xor rax, rcx
            rcx = 0x6A01EB295C695943;               //mov rcx, 0x6A01EB295C695943
            rax *= rcx;             //imul rax, rcx
            rcx = 0x9BBE6575DCB15C28;               //mov rcx, 0x9BBE6575DCB15C28
            rax ^= rcx;             //xor rax, rcx
            rax *= read<uintptr_t>(rdx + 0x19);             //imul rax, [rdx+0x19]
            rcx = 0x9A76A1C3B04C8361;               //mov rcx, 0x9A76A1C3B04C8361
            rax *= rcx;             //imul rax, rcx
            rax -= rbx;             //sub rax, rbx
            return { rax, currentCase };
        }
        case 13:
        {
            r10 = read<uintptr_t>(globals.base_address + 0x970810E);                //mov r10, [0x0000000005E7C3C7]
            rbx = globals.base_address;             //lea rbx, [0xFFFFFFFFFC7742B2]
            rax ^= rbx;             //xor rax, rbx
            rcx = 0x424E7D3CE7A4BDA3;               //mov rcx, 0x424E7D3CE7A4BDA3
            rax *= rcx;             //imul rax, rcx
            rcx = globals.base_address + 0x1A767856;                //lea rcx, [0x0000000016EDB6E9]
            rcx = ~rcx;             //not rcx
            rcx ^= r11;             //xor rcx, r11
            rax += rcx;             //add rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0xF;            //shr rcx, 0x0F
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x1E;           //shr rcx, 0x1E
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x3C;           //shr rcx, 0x3C
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x1;            //shr rcx, 0x01
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x2;            //shr rcx, 0x02
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x4;            //shr rcx, 0x04
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x8;            //shr rcx, 0x08
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x10;           //shr rcx, 0x10
            rax ^= rcx;             //xor rax, rcx
            rdx = 0;                //and rdx, 0xFFFFFFFFC0000000
            rdx = _rotl64(rdx, 0x10);               //rol rdx, 0x10
            rcx = rax;              //mov rcx, rax
            rdx ^= r10;             //xor rdx, r10
            rcx >>= 0x20;           //shr rcx, 0x20
            rdx = ~rdx;             //not rdx
            rax ^= rcx;             //xor rax, rcx
            rax *= read<uintptr_t>(rdx + 0x19);             //imul rax, [rdx+0x19]
            uintptr_t RSP_0xFFFFFFFFFFFFFFAF;
            RSP_0xFFFFFFFFFFFFFFAF = 0x8DB30096C278A251;            //mov rcx, 0x8DB30096C278A251 : RBP+0xFFFFFFFFFFFFFFAF
            rax *= RSP_0xFFFFFFFFFFFFFFAF;          //imul rax, [rbp-0x51]
            rcx = rbx + 0x31eb9108;                 //lea rcx, [rbx+0x31EB9108]
            rcx += r11;             //add rcx, r11
            rax ^= rcx;             //xor rax, rcx
            return { rax, currentCase };
        }
        case 14:
        {
            r10 = read<uintptr_t>(globals.base_address + 0x970810E);                //mov r10, [0x0000000005E7BECB]
            rbx = globals.base_address;             //lea rbx, [0xFFFFFFFFFC773DB6]
            rdx = rbx + 0xa832;             //lea rdx, [rbx+0xA832]
            rdx += r11;             //add rdx, r11
            rdx ^= rax;             //xor rdx, rax
            rax = r11;              //mov rax, r11
            rax = ~rax;             //not rax
            rax += rdx;             //add rax, rdx
            rax -= rbx;             //sub rax, rbx
            rax -= 0x2FECE2F9;              //sub rax, 0x2FECE2F9
            rcx = rbx + 0x4948;             //lea rcx, [rbx+0x4948]
            rcx += r11;             //add rcx, r11
            rax += rcx;             //add rax, rcx
            rcx = 0xE986304E17E64F7D;               //mov rcx, 0xE986304E17E64F7D
            rax *= rcx;             //imul rax, rcx
            rcx = globals.base_address + 0x5BB7;            //lea rcx, [0xFFFFFFFFFC7797D9]
            rcx = ~rcx;             //not rcx
            rcx -= r11;             //sub rcx, r11
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x1A;           //shr rcx, 0x1A
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x34;           //shr rcx, 0x34
            rax ^= rcx;             //xor rax, rcx
            rcx = 0;                //and rcx, 0xFFFFFFFFC0000000
            rcx = _rotl64(rcx, 0x10);               //rol rcx, 0x10
            rcx ^= r10;             //xor rcx, r10
            rcx = ~rcx;             //not rcx
            rax *= read<uintptr_t>(rcx + 0x19);             //imul rax, [rcx+0x19]
            rcx = r11;              //mov rcx, r11
            rcx = ~rcx;             //not rcx
            uintptr_t RSP_0xFFFFFFFFFFFFFFA7;
            RSP_0xFFFFFFFFFFFFFFA7 = globals.base_address + 0x30A5920C;             //lea rcx, [0x000000002D1CCFA1] : RBP+0xFFFFFFFFFFFFFFA7
            rcx ^= RSP_0xFFFFFFFFFFFFFFA7;          //xor rcx, [rbp-0x59]
            rax += rcx;             //add rax, rcx
            return { rax, currentCase };
        }
        case 15:
        {
            r10 = read<uintptr_t>(globals.base_address + 0x970810E);                //mov r10, [0x0000000005E7B99E]
            rbx = globals.base_address;             //lea rbx, [0xFFFFFFFFFC773889]
            rdx = globals.base_address + 0x56C36699;                //lea rdx, [0x00000000533A9E75]
            rcx = 0x14288A7031FA1D2A;               //mov rcx, 0x14288A7031FA1D2A
            rax += rcx;             //add rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0xA;            //shr rcx, 0x0A
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x14;           //shr rcx, 0x14
            rax ^= rcx;             //xor rax, rcx
            rcx = rax;              //mov rcx, rax
            rcx >>= 0x28;           //shr rcx, 0x28
            rax ^= rcx;             //xor rax, rcx
            rcx = 0xF471F0FE111CB275;               //mov rcx, 0xF471F0FE111CB275
            rax *= rcx;             //imul rax, rcx
            rcx = rdx;              //mov rcx, rdx
            rcx = ~rcx;             //not rcx
            rcx *= r11;             //imul rcx, r11
            rax ^= rcx;             //xor rax, rcx
            rax -= r11;             //sub rax, r11
            rax -= rbx;             //sub rax, rbx
            rax -= 0x57EC1422;              //sub rax, 0x57EC1422
            rcx = r11;              //mov rcx, r11
            uintptr_t RSP_0x7;
            RSP_0x7 = globals.base_address + 0xF4C;                 //lea rcx, [0xFFFFFFFFFC774796] : RBP+0x7
            rcx *= RSP_0x7;                 //imul rcx, [rbp+0x07]
            rax += rcx;             //add rax, rcx
            rcx = 0;                //and rcx, 0xFFFFFFFFC0000000
            rcx = _rotl64(rcx, 0x10);               //rol rcx, 0x10
            rcx ^= r10;             //xor rcx, r10
            rcx = ~rcx;             //not rcx
            rax *= read<uintptr_t>(rcx + 0x19);             //imul rax, [rcx+0x19]
            rcx = globals.base_address + 0x1793;            //lea rcx, [0xFFFFFFFFFC774CEB]
            rcx -= r11;             //sub rcx, r11
            rax ^= rcx;             //xor rax, rcx
            return { rax, currentCase };
        }
        }
    }